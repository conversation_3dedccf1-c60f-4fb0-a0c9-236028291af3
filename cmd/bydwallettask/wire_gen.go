// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	tron2 "byd_wallet/internal/biz/gaspool/paymaster/tron"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/data/covalenthq"
	"byd_wallet/internal/data/metapath"
	"byd_wallet/internal/server"
	"byd_wallet/internal/service/mq"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	coinDataThirdAPI, err := data.NewCoinDataThirdAPI(dataData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	currencyRateAPI, err := data.NewCurrencyRateAPI(dataData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	syncer := coindata.NewSyncer(logger, coinDataThirdAPI, db, universalClient, currencyRateAPI)
	swapRepo := data.NewSwapRepo(dataData)
	config, err := data.NewMetapathConfig(dataData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	client := metapath.NewClient(config, logger)
	rpcEndpointRepo := data.NewRPCEndpointRepo(dataData)
	roundRobinClient, cleanup3, err := data.NewTronChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenSwapper := metapath.NewTokenSwapper(client, roundRobinClient, logger)
	okxClient, err := data.NewOKXClient(dataData, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	okxChainIndexDto := data.NewOKXChainIndexDto()
	tokenAssetRepo := data.NewTokenAssetRepo(logger, dataData, okxClient, okxChainIndexDto)
	transactionRepo := data.NewTransactionRepo(dataData)
	swapTokenFetcher := metapath.NewSwapTokenFetcher(client, logger)
	multiChainClient, cleanup4, err := data.NewEvmChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	smartNodeSelectionClient, cleanup5, err := data.NewSolanaChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenContractRepo := data.NewTokenContractRepo(logger, multiChainClient, roundRobinClient, smartNodeSelectionClient)
	s3Repo, err := data.NewS3Repo(dataData)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenCollector := biz.NewTokenCollector(tokenAssetRepo, tokenContractRepo, s3Repo)
	blockchainNetworkRepo := data.NewBlockchainNetworkRepo(dataData)
	transactionFetcher := tron.NewTransactionFetcher(roundRobinClient)
	bizTransactionFetcher := biz.NewTransactionFetcher(transactionFetcher)
	swapUsecase := biz.NewSwapUsecase(logger, swapRepo, tokenSwapper, tokenAssetRepo, transactionRepo, swapTokenFetcher, tokenCollector, coinDataThirdAPI, blockchainNetworkRepo, bizTransactionFetcher)
	timerServer, err := server.NewTimerServer(logger, syncer, swapUsecase, swapRepo)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenAssetUsecase := biz.NewTokenAssetUsecase(logger, tokenAssetRepo)
	tokenContractUsecase := biz.NewTokenContractUsecase(logger, tokenContractRepo)
	tokenAssetService := mq.NewTokenAssetService(tokenAssetUsecase, tokenContractUsecase)
	userAddressRepo := data.NewUserAddressRepo(dataData)
	userAddressUsecase := biz.NewUserAddressUsecase(logger, userAddressRepo)
	userHoldTokenRepo := data.NewUserHoldTokenRepo(dataData)
	userHoldTokenUsecase := biz.NewUserHoldTokenUsecase(userHoldTokenRepo, logger)
	userHoldTokenService := mq.NewUserHoldTokenService(tokenAssetUsecase, userAddressUsecase, userHoldTokenUsecase)
	approvalRepo := data.NewApprovalRepo(dataData)
	covalenthqClient, err := data.NewCovalenthqClient(dataData, logger)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	approvalFetcher := covalenthq.NewApprovalFetcher(covalenthqClient)
	approvalUsecase := biz.NewApprovalUsecase(approvalRepo, blockchainNetworkRepo, approvalFetcher)
	userRegisterService := mq.NewUserRegisterService(approvalUsecase)
	mqServer, cleanup6, err := server.NewMQServer(confServer, logger, tokenAssetService, userHoldTokenService, userRegisterService)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	spotPriceRepo, err := data.NewSpotPriceRepo(logger, dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	spotPriceManager := biz.NewSpotPriceManager(logger, spotPriceRepo)
	hotAccountReader := data.NewGasPoolHotAccountReader(dataData)
	gasPoolRepo := data.NewGasPoolRepo(dataData)
	tronBandwidthPayWallet, err := data.NewTronBandwidthPayWallet(dataData, roundRobinClient)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	weidubotClient, err := data.NewWeidubotClient(logger, dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronRentApi := data.NewTronRentApi(dataData, weidubotClient)
	tronAddressChecker := data.NewTronAddressChecker(dataData, roundRobinClient)
	asyncTaskMgr := data.NewTronAsyncTaskMgr(dataData)
	paymaster := tron2.NewPaymaster(logger, spotPriceRepo, hotAccountReader, gasPoolRepo, gasPoolRepo, roundRobinClient, tronBandwidthPayWallet, tronRentApi, tronAddressChecker, asyncTaskMgr)
	repoFactory := data.NewEvmPaymasterRepoFactory(dataData)
	paymasterBuilder := evm.NewPaymasterBuilder(logger, spotPriceRepo, hotAccountReader, multiChainClient, repoFactory, gasPoolRepo, gasPoolRepo)
	evmPaymaster := biz.NewEvmPaymasterForAsync(paymasterBuilder)
	asyncServer := server.NewAsyncServer(logger, spotPriceManager, paymaster, evmPaymaster)
	app := newApp(logger, timerServer, mqServer, asyncServer)
	return app, func() {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
