package evm

import (
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// async.go - EVM paymaster 异步任务处理相关功能
//
// 核心功能：
// 1. 异步gas转账确认处理：确保用户只有在gas费用到账后才发送离线签名的交易
// 2. 两阶段交易处理：先确认gas转账，再提交用户的离线签名交易
// 3. 多交易类型支持：区分处理用户转账(SendGasTxn)和充值交易(DepositTxn)
// 4. 完整错误处理：包括超时处理、失败退款、状态转换等
// 5. 多链EVM兼容：支持Ethereum、BSC、Polygon、Arbitrum、Optimism、Base等
//
// 工作流程：
// 1. 用户提交离线签名交易 -> 系统先转账gas费用给用户地址
// 2. 创建等待确认记录 -> 异步监控gas转账状态
// 3. gas转账确认后 -> 验证并提交用户的离线签名交易
// 4. 更新交易状态 -> 等待用户交易确认完成
// 5. 异常处理 -> 超时、失败时执行退款和状态回滚
//
// 设计原则：
// - 确保交易安全：gas费用确认后才执行用户交易
// - 状态一致性：完整的状态转换和错误恢复机制
// - 资源管理：及时清理等待确认记录，避免资源泄漏
// - 多链兼容：统一的接口支持所有EVM兼容链
// - 错误处理：英文错误信息，中文日志注释

// handleGasTransferConfirm 处理gas转账确认和用户交易发送
//
// 功能说明：
// 1. 异步处理EVM链的gas转账确认，确保用户只有在gas费用到账后才发送离线签名的交易
// 2. 支持两种交易类型：SendGasTxn（用户转账）和DepositTxn（充值交易）
// 3. 实现完整的错误处理和状态转换，包括失败时的gas pool退款机制
// 4. 确保交易状态的一致性和数据完整性，支持多链EVM兼容
// 5. 处理交易超时和过期情况，避免资源泄漏
//
// 参数:
//   - ctx: 上下文对象
//   - gasTransferWaitSecs: gas转账等待确认时间（秒）
func (pm *Paymaster) handleGasTransferConfirm(ctx context.Context, gasTransferWaitSecs int64) {
	// 获取所有等待确认的gas转账记录
	records, err := pm.repo.AllEvmGasTransferWaitConfirmRecord(ctx)
	if err != nil {
		pm.log.Errorf("获取%s WaitConfirmRecord error: %v", pm.chainName, err)
		return
	}

	// 如果没有待处理记录，直接返回
	if len(records) == 0 {
		return
	}

	pm.log.Debugf("%s链处理gas转账确认: %d条记录", pm.chainName, len(records))
	nowUnix := time.Now().Unix()

	// 定义超时时间（30分钟）
	const gasTransferTimeoutSecs int64 = 1800

	fmt.Println("len(records) = ", len(records))
	// 遍历处理每条等待确认的记录
	for _, record := range records {
		spent := nowUnix - record.TimeUnix

		// 检查交易是否已超时
		if spent > gasTransferTimeoutSecs {
			pm.log.Warnf("%s链gas转账等待超时，交易ID: %d，等待时间: %d秒",
				pm.chainName, record.TxID, spent)
			pm.handleGasTransferTimeout(ctx, record)
			continue
		}

		// 检查是否还需要等待（避免过于频繁的区块链查询）
		if spent < gasTransferWaitSecs {
			continue
		}

		// 根据交易类型分别处理，确保逻辑清晰分离
		switch record.TxnType {
		case SendGasTxn:
			// 处理用户转账交易的gas确认
			pm.handleSendGasTransferConfirm(ctx, record)
		case WaitSenderTxn:
			// 处理用户转账交易的gas确认
			pm.handleWaiterSendTransferConfirm(ctx, record)
		case DepositTxn:
			// 处理充值交易的gas确认
			pm.handleDepositTransferConfirm(ctx, record)
		default:
			pm.log.Warnf("%s链发现未知交易类型: %s，交易ID: %d",
				pm.chainName, record.TxnType, record.TxID)
			// 清理未知类型的记录
			if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
				pm.log.Errorf("删除%s链未知类型等待确认记录失败: %v", pm.chainName, err)
			}
		}
	}
}

// handleSendGasTransferConfirm 处理用户转账交易的gas转账确认
//
// 工作流程：
// 1. 验证sponsor交易状态是否为WaitGas
// 2. 检查gas转账交易是否已在区块链上确认
// 3. gas确认后，验证并发送用户的离线签名交易到区块链
// 4. 更新交易状态为Pending，等待用户交易确认
// 5. 处理各种失败场景，包括gas pool退款
//
// 参数:
//   - ctx: 上下文对象
//   - record: gas转账等待确认记录
func (pm *Paymaster) handleSendGasTransferConfirm(ctx context.Context, record *EvmGasTransferWaitConfirmRecord) {
	// 获取sponsor交易数据
	stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, record.TxID)
	if err != nil {
		pm.log.Errorf("获取%s链sponsor交易失败: %v", pm.chainName, err)
		// 数据库错误，保留记录等待下次处理
		return
	}

	// 检查交易状态是否仍为等待gas状态
	if stx.Status != model.GasPoolTxStatusWaitGas {
		pm.log.Debugf("%s链交易状态已变更，删除等待确认记录，交易ID: %d，状态: %s",
			pm.chainName, record.TxID, stx.Status)
		// 状态已变更，清理等待确认记录
		if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
			pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
		}
		return
	}

	// 检查gas转账交易是否已在区块链上确认
	confirmed, err := pm.checkGasTransferConfirmed(ctx, record.ChainIndex, record.TxHash)
	if err != nil {
		pm.log.Errorf("检查%s链gas转账确认状态失败，交易哈希: %s，错误: %v",
			pm.chainName, record.TxHash, err)
		// 网络错误，保留记录等待下次处理
		return
	}

	if !confirmed {
		pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
			pm.chainName, record.TxHash)
		return
	}

	// gas转账已确认，开始处理用户的离线签名交易
	pm.log.Infof("%s链gas转账已确认，开始验证并发送用户离线签名交易，交易ID: %d，gas转账哈希: %s",
		pm.chainName, record.TxID, record.TxHash)

	// 验证并发送用户的离线签名交易
	if err := pm.processOfflineSignedUserTransaction(ctx, stx, record.TxID); err != nil {
		pm.log.Errorf("%s链处理用户离线签名交易失败，交易ID: %d，错误: %v",
			pm.chainName, record.TxID, err)
		// 处理失败，标记交易为失败状态并退款
		pm.handleUserTransactionFailure(ctx, stx, record.TxID,
			fmt.Sprintf("failed to process offline signed user transaction: %v", err))
		return
	}

	pm.log.Infof("%s链用户离线签名交易处理成功，交易ID: %d", pm.chainName, record.TxID)
}

func (pm *Paymaster) handleWaiterSendTransferConfirm(ctx context.Context, record *EvmGasTransferWaitConfirmRecord) {
	// 获取sponsor交易数据
	stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, record.TxID)
	if err != nil {
		pm.log.Errorf("获取%s链sponsor交易失败: %v", pm.chainName, err)
		// 数据库错误，保留记录等待下次处理
		return
	}

	// 检查交易状态是否仍为等待gas状态
	if stx.Status != model.GasPoolTxStatusPending {
		pm.log.Debugf("%s链交易状态已变更，删除等待确认记录，交易ID: %d，状态: %s",
			pm.chainName, record.TxID, stx.Status)

		return
	}

	// 检查gas转账交易是否已在区块链上确认
	confirmed, err := pm.checkGasTransferConfirmed(ctx, record.ChainIndex, record.TxHash)
	if err != nil {
		pm.log.Errorf("检查%s链gas转账确认状态失败，交易哈希: %s，错误: %v",
			pm.chainName, record.TxHash, err)
		// 网络错误，保留记录等待下次处理
		return
	}

	if !confirmed {
		pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
			pm.chainName, record.TxHash)
		return
	}

	// gas转账已确认，开始处理用户的离线签名交易
	pm.log.Infof("%s链gas转账已确认，开始验证并发送用户离线签名交易，交易ID: %d，gas转账哈希: %s",
		pm.chainName, record.TxID, record.TxHash)
	// 充值成功，更新交易状态为Success
	updateStatus := &model.GasPoolSponsorTx{
		Model:  stx.Model,
		Status: model.GasPoolTxStatusSuccess,
	}
	err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, updateStatus)
	if err != nil {
		pm.log.Debugf("%s链gas转账跟新状态失败，继续等待，交易哈希: %s",
			pm.chainName, record.TxHash)
		return
	}
	if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
		pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
	}
}

// handleDepositTransferConfirm 处理充值交易的gas转账确认
//
// 工作流程：
// 1. 验证sponsor交易状态是否为Pending
// 2. 检查gas转账交易是否已在区块链上确认
// 3. gas确认后，执行gas pool充值操作
// 4. 更新交易状态为Success
// 5. 处理各种失败场景
//
// 参数:
//   - ctx: 上下文对象
//   - record: gas转账等待确认记录
func (pm *Paymaster) handleDepositTransferConfirm(ctx context.Context, record *EvmGasTransferWaitConfirmRecord) {
	// 获取sponsor交易数据
	stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, record.TxID)
	if err != nil {
		pm.log.Errorf("获取%s链sponsor交易失败: %v", pm.chainName, err)
		return
	}

	// 检查交易状态是否仍为Pending状态
	if stx.Status != model.GasPoolTxStatusPending {
		pm.log.Debugf("%s链交易状态已变更，删除等待确认记录，交易ID: %d，状态: %s",
			pm.chainName, record.TxID, stx.Status)
		// 状态已变更，清理等待确认记录
		if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
			pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
		}
		return
	}

	// 检查gas转账交易是否已在区块链上确认
	confirmed, err := pm.checkGasTransferConfirmed(ctx, record.ChainIndex, record.TxHash)
	if err != nil {
		pm.log.Errorf("检查%s链gas转账确认状态失败，交易哈希: %s，错误: %v",
			pm.chainName, record.TxHash, err)
		return
	}

	if !confirmed {
		pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
			pm.chainName, record.TxHash)
		return
	}

	// gas转账已确认，执行充值操作
	pm.log.Infof("%s链gas转账已确认，开始执行充值操作，交易ID: %d，gas转账哈希: %s",
		pm.chainName, record.TxID, record.TxHash)

	// 执行gas pool充值
	depositFlow, err := pm.gpMgr.DepositGasPool(ctx, stx.UserID, stx.ValueUSDT, stx.ChainIndex, record.TxHash)
	if err != nil {
		pm.log.Errorf("%s链执行gas pool充值失败: %v，交易ID: %d",
			pm.chainName, err, record.TxID)
		// 充值失败，标记交易为失败状态
		pm.handleDepositTransactionFailure(ctx, stx, record.TxID,
			fmt.Sprintf("failed to deposit gas pool: %v", err))
		return
	}

	// 充值成功，更新交易状态为Success
	updateStatus := &model.GasPoolSponsorTx{
		Model:         stx.Model,
		Status:        model.GasPoolTxStatusSuccess,
		DepositFlowID: depositFlow.ID,
	}

	if err := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, updateStatus); err != nil {
		pm.log.Errorf("更新%s链sponsor交易状态失败: %v，交易ID: %d",
			pm.chainName, err, record.TxID)
		// 注意：此时充值已完成，状态更新失败不影响充值结果
	}

	// 清理等待确认记录
	if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
		pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
	}

	pm.log.Infof("%s链充值操作完成，交易ID: %d，充值流水ID: %d",
		pm.chainName, record.TxID, depositFlow.ID)
}

// processOfflineSignedUserTransaction 处理用户的离线签名交易
//
// 功能说明：
// 1. 验证用户提供的离线签名交易数据的完整性和有效性
// 2. 解码RLP编码的交易数据并进行基本验证
// 3. 验证交易签名和发送者地址
// 4. 发送交易到区块链网络
// 5. 更新sponsor交易状态为Pending
//
// 参数:
//   - ctx: 上下文对象
//   - stx: sponsor交易数据
//   - txID: 交易ID
//
// 返回值:
//   - error: 处理过程中的错误信息
func (pm *Paymaster) processOfflineSignedUserTransaction(ctx context.Context, stx *model.GasPoolSponsorTx, txID uint) error {
	// 验证原始交易数据是否存在
	if stx.RawTxHex == "" {
		return fmt.Errorf("missing offline signed transaction data")
	}

	// 解码用户的离线签名交易数据
	evmTx, err := utils.RlpDecodeBytes(stx.RawTxHex)
	if err != nil {
		return fmt.Errorf("failed to decode offline signed transaction: %w", err)
	}

	// 基本验证：检查交易是否已签名
	if evmTx.ChainId() == nil {
		return fmt.Errorf("transaction missing chain ID")
	}

	// 验证交易签名和发送者地址
	if err := pm.validateOfflineSignedTransaction(ctx, evmTx, stx); err != nil {
		return fmt.Errorf("transaction validation failed: %w", err)
	}

	// 发送用户的离线签名交易到区块链网络
	userTxHash, err := pm.sendTransaction(ctx, evmTx)
	if err != nil {
		return fmt.Errorf("failed to send offline signed transaction to blockchain: %w", err)
	}

	// 用户交易发送成功，更新状态为Pending
	updateStatus := &model.GasPoolSponsorTx{
		Model:  stx.Model,
		Status: model.GasPoolTxStatusPending,
		TxHash: userTxHash,
	}

	if err := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, updateStatus); err != nil {
		pm.log.Errorf("更新%s链sponsor交易状态失败: %v，交易ID: %d",
			pm.chainName, err, txID)
		// 注意：此时用户交易已发送，但状态更新失败
		// 这种情况下不应该返回错误，因为交易可能会成功
		// 只记录日志，让后续的确认流程处理
	}
	pm.log.Infof("%s链用户离线签名交易发送成功，交易ID: %d，用户交易哈希: %s",
		pm.chainName, txID, userTxHash)
	var record EvmGasTransferWaitConfirmRecord
	record.TxID = txID
	record.TxHash = userTxHash
	record.TxnType = WaitSenderTxn
	record.TimeUnix = time.Now().Unix()
	err = pm.repo.CreateEvmGasTransferWaitConfirmRecord(context.Background(), &record)
	if err != nil {
		pm.log.Errorf("创建%s链等待代发更新状态redis失败: %v，交易ID: %d",
			pm.chainName, err, txID)
	}

	return nil
}

// validateOfflineSignedTransaction 验证用户的离线签名交易
//
// 功能说明：
// 1. 验证交易签名的有效性
// 2. 检查发送者地址是否与预期一致
// 3. 验证交易基本参数（nonce、gas limit等）
// 4. 确保交易符合当前链的要求
//
// 参数:
//   - ctx: 上下文对象
//   - evmTx: 解码后的EVM交易
//   - stx: sponsor交易数据
//
// 返回值:
//   - error: 验证失败的错误信息
func (pm *Paymaster) validateOfflineSignedTransaction(ctx context.Context, evmTx *types.Transaction, stx *model.GasPoolSponsorTx) error {
	// 获取EVM客户端用于验证
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return fmt.Errorf("failed to get EVM client: %w", err)
	}

	// 从交易中恢复发送者地址
	signer := types.LatestSignerForChainID(evmTx.ChainId())
	sender, err := types.Sender(signer, evmTx)
	if err != nil {
		return fmt.Errorf("failed to recover sender address: %w", err)
	}

	// 记录发送者地址用于日志追踪
	pm.log.Debugf("%s链交易发送者地址: %s", pm.chainName, sender.Hex())

	// 验证交易基本参数
	if evmTx.Gas() == 0 {
		return fmt.Errorf("transaction gas limit cannot be zero")
	}

	if evmTx.GasPrice() == nil || evmTx.GasPrice().Cmp(big.NewInt(0)) <= 0 {
		return fmt.Errorf("transaction gas price must be positive")
	}

	// 检查发送者账户余额是否足够支付gas费用
	balance, err := client.BalanceAt(ctx, sender, nil)
	if err != nil {
		pm.log.Warnf("无法获取发送者余额进行验证，地址: %s，错误: %v", sender.Hex(), err)
		// 余额检查失败不阻止交易，因为可能是网络问题
		// 让区块链网络自己验证
	} else {
		// 计算交易所需的最大费用
		maxFee := new(big.Int).Mul(new(big.Int).SetUint64(evmTx.Gas()), evmTx.GasPrice())
		if evmTx.Value() != nil {
			maxFee.Add(maxFee, evmTx.Value())
		}

		if balance.Cmp(maxFee) < 0 {
			return fmt.Errorf("insufficient balance: required %s, available %s",
				maxFee.String(), balance.String())
		}
	}

	pm.log.Debugf("%s链离线签名交易验证通过，发送者: %s，gas: %d，gasPrice: %s",
		pm.chainName, sender.Hex(), evmTx.Gas(), evmTx.GasPrice().String())

	return nil
}

// handleUserTransactionFailure 处理用户交易失败的情况
//
// 功能说明：
// 1. 将sponsor交易状态标记为失败
// 2. 如果之前有扣减gas pool，则执行退款操作
// 3. 清理等待确认记录
// 4. 记录详细的失败原因
//
// 参数:
//   - ctx: 上下文对象
//   - stx: sponsor交易数据
//   - txID: 交易ID
//   - reason: 失败原因
func (pm *Paymaster) handleUserTransactionFailure(ctx context.Context, stx *model.GasPoolSponsorTx, txID uint, reason string) {
	pm.log.Errorf("%s链用户交易处理失败，交易ID: %d，原因: %s", pm.chainName, txID, reason)

	// 更新交易状态为失败
	updateStatus := &model.GasPoolSponsorTx{
		Model:  stx.Model,
		Status: model.GasPoolTxStatusFail,
		Reason: reason,
	}

	// 如果之前有扣减gas pool且不是预扣减类型，则需要退款
	if !stx.TxType.IsPreReduceGasPool() && stx.ReduceFlowID > 0 {
		refundFlow, refundErr := pm.gpMgr.RefundGasPool(ctx, stx.ReduceFlowID, stx.GasUSDT)
		if refundErr != nil {
			pm.log.Errorf("%s链退款gas pool失败，交易ID: %d，错误: %v",
				pm.chainName, txID, refundErr)
			// 退款失败也要记录，但不影响状态更新
			updateStatus.Reason = fmt.Sprintf("%s; refund failed: %v", reason, refundErr)
		} else {
			updateStatus.RefundFlowID = refundFlow.ID
			pm.log.Infof("%s链gas pool退款成功，交易ID: %d，退款流水ID: %d",
				pm.chainName, txID, refundFlow.ID)
		}
	}

	// 更新sponsor交易状态
	if err := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, updateStatus); err != nil {
		pm.log.Errorf("更新%s链sponsor交易失败状态失败: %v，交易ID: %d",
			pm.chainName, err, txID)
	}

	// 清理等待确认记录
	if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, txID); err != nil {
		pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
	}
}

// handleDepositTransactionFailure 处理充值交易失败的情况
//
// 功能说明：
// 1. 将sponsor交易状态标记为失败
// 2. 清理等待确认记录
// 3. 记录详细的失败原因
// 4. 充值交易失败通常不需要退款，因为资金还未进入gas pool
//
// 参数:
//   - ctx: 上下文对象
//   - stx: sponsor交易数据
//   - txID: 交易ID
//   - reason: 失败原因
func (pm *Paymaster) handleDepositTransactionFailure(ctx context.Context, stx *model.GasPoolSponsorTx, txID uint, reason string) {
	pm.log.Errorf("%s链充值交易处理失败，交易ID: %d，原因: %s", pm.chainName, txID, reason)

	// 更新交易状态为失败
	updateStatus := &model.GasPoolSponsorTx{
		Model:  stx.Model,
		Status: model.GasPoolTxStatusFail,
		Reason: reason,
	}

	// 更新sponsor交易状态
	if err := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, updateStatus); err != nil {
		pm.log.Errorf("更新%s链sponsor交易失败状态失败: %v，交易ID: %d",
			pm.chainName, err, txID)
	}

	// 清理等待确认记录
	if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, txID); err != nil {
		pm.log.Errorf("删除%s链gas转账等待确认记录失败: %v", pm.chainName, err)
	}
}

// checkGasTransferConfirmed 检查gas转账交易是否已确认
//
// 功能说明：
// 1. 通过EVM客户端查询指定交易的收据信息
// 2. 验证交易是否已被区块链网络确认且执行成功
// 3. 支持多链EVM兼容，根据chainIndex选择对应的客户端
// 4. 处理交易未打包、执行失败等各种情况
//
// 参数:
//   - ctx: 上下文对象
//   - chainIndex: 链索引，用于选择对应的EVM客户端
//   - txHash: gas转账交易哈希
//
// 返回值:
//   - bool: 是否已确认（true表示已确认且成功）
//   - error: 错误信息（英文）
func (pm *Paymaster) checkGasTransferConfirmed(ctx context.Context, chainIndex int64, txHash string) (bool, error) {
	// 获取对应链的EVM客户端
	client, err := pm.evmCli.Select(chainIndex)
	if err != nil {
		return false, fmt.Errorf("failed to get EVM client for chain %s: %w", pm.chainName, err)
	}

	// 获取交易收据以检查确认状态
	receipt, err := client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		// 如果交易还未被打包，返回false但不报错
		// 这是正常情况，需要继续等待
		pm.log.Debugf("%s链gas转账交易尚未被打包，交易哈希: %s", pm.chainName, txHash)
		return false, nil
	}

	// 检查交易执行状态（1表示成功，0表示失败）
	if receipt.Status == 1 {
		pm.log.Debugf("%s链gas转账交易确认成功，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return true, nil
	} else {
		// 交易已确认但执行失败
		pm.log.Errorf("%s链gas转账交易执行失败，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return false, fmt.Errorf("gas transfer transaction execution failed")
	}
}

// handleGasTransferTimeout 处理gas转账超时的情况
//
// 功能说明：
// 1. 处理等待gas转账确认超时的记录
// 2. 将相关的sponsor交易标记为失败状态
// 3. 执行gas pool退款（如果适用）
// 4. 清理等待确认记录，避免资源泄漏
//
// 参数:
//   - ctx: 上下文对象
//   - record: 超时的gas转账等待确认记录
func (pm *Paymaster) handleGasTransferTimeout(ctx context.Context, record *EvmGasTransferWaitConfirmRecord) {
	pm.log.Errorf("%s链gas转账确认超时，交易ID: %d，交易哈希: %s",
		pm.chainName, record.TxID, record.TxHash)

	// 获取sponsor交易数据
	stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, record.TxID)
	if err != nil {
		pm.log.Errorf("获取%s链超时sponsor交易失败: %v", pm.chainName, err)
		// 即使获取失败，也要清理等待确认记录
		if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
			pm.log.Errorf("删除%s链超时gas转账等待确认记录失败: %v", pm.chainName, err)
		}
		return
	}

	// 根据交易类型处理超时
	switch record.TxnType {
	case SendGasTxn:
		// 用户转账交易超时，需要退款
		pm.handleUserTransactionFailure(ctx, stx, record.TxID,
			"gas transfer confirmation timeout")
	case DepositTxn:
		// 充值交易超时，标记为失败
		pm.handleDepositTransactionFailure(ctx, stx, record.TxID,
			"gas transfer confirmation timeout")
	default:
		// 未知类型，直接清理记录
		pm.log.Warnf("%s链超时记录为未知交易类型: %s，交易ID: %d",
			pm.chainName, record.TxnType, record.TxID)
		if err := pm.repo.DeleteEvmGasTransferWaitConfirmRecord(ctx, record.TxID); err != nil {
			pm.log.Errorf("删除%s链超时gas转账等待确认记录失败: %v", pm.chainName, err)
		}
	}
}
