package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

// paymaster.go - EVM paymaster 主文件
// 包含 paymaster 结构体定义、构造函数和核心接口实现
// 支持 Ethereum、Polygon、Arbitrum、Optimism、Base 等主流 EVM 兼容链

const (
	// DefaultPriceExpireSeconds 默认价格过期时间（秒）
	DefaultPriceExpireSeconds = 300 // 5分钟
	// DefaultGasLimitMultiplier 默认gas limit倍数
	DefaultGasLimitMultiplier = 1.2
	// DefaultGasTransferWaitSeconds 默认gas转账等待确认时间（秒）
	DefaultGasTransferWaitSeconds = 2     // 2秒
	DefaultGasLimit               = 26000 // 2秒

)

type TxnType string

const (
	SendGasTxn    TxnType = "SendGas"
	WaitSenderTxn TxnType = "WaitSender"
	DepositTxn    TxnType = "Deposit"
)

// EvmGasTransferWaitConfirmRecord EVM gas转账等待确认记录
type EvmGasTransferWaitConfirmRecord struct {
	ChainIndex int64   // chainIndex
	TxID       uint    // sponsor交易ID
	TxnType    TxnType // hash 类型
	TxHash     string  // gas转账交易哈希
	TimeUnix   int64   // 创建时间
}

// Repo EVM paymaster数据访问接口
type Repo interface {
	// CreateEvmGasTransferWaitConfirmRecord 创建EVM gas转账等待确认记录
	CreateEvmGasTransferWaitConfirmRecord(ctx context.Context, record *EvmGasTransferWaitConfirmRecord) error
	// AllEvmGasTransferWaitConfirmRecord 获取所有EVM gas转账等待确认记录
	AllEvmGasTransferWaitConfirmRecord(ctx context.Context) ([]*EvmGasTransferWaitConfirmRecord, error)
	// DeleteEvmGasTransferWaitConfirmRecord 删除EVM gas转账等待确认记录
	DeleteEvmGasTransferWaitConfirmRecord(ctx context.Context, txID uint) error
}

// RepoFactory EVM paymaster repo工厂函数类型
// 用于为不同的链索引创建独立的repo实例
type RepoFactory func(chainIndex int64) Repo

// PaymasterBuilder EVM paymaster构建器
// 支持为每个EVM链创建独立的repo实例，避免不同链之间的数据混淆
type PaymasterBuilder struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	evmCli           *evm.MultiChainClient
	repoFactory      RepoFactory // repo工厂函数，为每个链创建独立的repo
	stxMgr           base.GasPoolSponsorTxMgr
	gpMgr            base.GasPoolMgr
}

// NewPaymasterBuilder 创建新的EVM paymaster构建器
// 参数:
//   - logger: 日志记录器
//   - tokenPriceReader: 代币价格读取器
//   - hotAccountReader: 热钱包账户读取器
//   - evmCli: EVM多链客户端
//   - repoFactory: repo工厂函数，用于为每个链创建独立的repo实例
//   - stxMgr: gas pool sponsor交易管理器
func NewPaymasterBuilder(
	logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	hotAccountReader base.HotAccountReader,
	evmCli *evm.MultiChainClient,
	repoFactory RepoFactory,
	stxMgr base.GasPoolSponsorTxMgr,
	gpMgr base.GasPoolMgr,
) *PaymasterBuilder {
	return &PaymasterBuilder{
		log:              log.NewHelper(logger),
		tokenPriceReader: tokenPriceReader,
		hotAccountReader: hotAccountReader,
		evmCli:           evmCli,
		repoFactory:      repoFactory,
		stxMgr:           stxMgr,
		gpMgr:            gpMgr,
	}
}

// Build 构建指定链的paymaster实例
// 为每个链创建独立的repo实例，确保不同链的数据隔离
func (b *PaymasterBuilder) Build(chainIndex int64) *Paymaster {
	chainName := constant.GetChainName(chainIndex)
	if chainName == "" {
		chainName = fmt.Sprintf("Chain_%d", chainIndex)
	}

	// 为当前链创建独立的repo实例
	chainRepo := b.repoFactory(chainIndex)

	// 创建基础paymaster实例
	pm := &Paymaster{
		log:                b.log,
		tokenPriceReader:   b.tokenPriceReader,
		hotAccountReader:   b.hotAccountReader,
		evmCli:             b.evmCli,
		repo:               chainRepo, // 使用链专用的repo实例
		stxMgr:             b.stxMgr,
		gpMgr:              b.gpMgr,
		chainIndex:         chainIndex,
		chainName:          chainName,
		priceExpireSeconds: DefaultPriceExpireSeconds,
		stopCh:             nil, // 在Start方法中初始化
	}

	// 为Optimism链初始化特有的oracle客户端
	if chainIndex == constant.OptimismChainIndex {
		client, err := b.evmCli.Select(chainIndex)
		if err != nil {
			b.log.Warnf("无法获取Optimism客户端，L1 data fee计算将使用默认值: %v", err)
		} else {
			optimismOracle, err := NewOptimismOracle(b.log.Logger(), client)
			if err != nil {
				b.log.Warnf("初始化Optimism oracle失败，L1 data fee计算将使用默认值: %v", err)
			} else {
				pm.optimismOracle = optimismOracle
				b.log.Infof("成功初始化Optimism oracle，支持精确的L1 data fee计算")
			}
		}
	}

	return pm
}

// Paymaster 统一的EVM paymaster实现
// 支持Ethereum、Polygon、Arbitrum、Optimism、Base等主流EVM兼容链
type Paymaster struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	evmCli           *evm.MultiChainClient
	repo             Repo
	stxMgr           base.GasPoolSponsorTxMgr
	gpMgr            base.GasPoolMgr
	// 链配置
	chainIndex int64
	chainName  string

	// 缓存字段，避免重复计算
	cachedAddress common.Address

	// 配置字段
	priceExpireSeconds int64 // 价格过期时间（秒）

	// Optimism特有组件（仅在Optimism链上使用）
	optimismOracle OptimismOracleInterface // Optimism gas price oracle客户端

	// 异步任务控制
	stopCh chan struct{}
}

func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (tx *gaspool.UserTx, err error) {
	return pm.decodeTransferTx(ctx, rawTxHex, txType)

}

func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	pm.log.Debugf("验证%s链用户交易签名，交易哈希: %s", pm.chainName, tx.TxHash)
	// 获取链ID
	chainID := constant.GetChainID(pm.chainIndex)
	if chainID == 0 {
		return false, fmt.Errorf("invalid chainIndex: %d", pm.chainIndex)
	}

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return false, err
	}

	// 验证签名
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return false, fmt.Errorf("s: %w", err)
	}

	cli, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return false, err
	}
	nonce, err := cli.NonceAt(ctx, sender, nil)
	if err != nil {
		return false, err
	}
	if nonce > evmTx.Nonce() {
		return false, fmt.Errorf("invalid noce: %v,chain nonce:%v", nonce, evmTx.Nonce())
	}
	pm.log.Debugf("%s链交易签名验证成功，发送者: %s", pm.chainName, sender.Hex())
	return true, nil

}

func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	// 输入验证
	if tx == nil {
		return nil, fmt.Errorf("chainIndex:%v invalid  tx", pm.chainIndex)
	}

	pm.log.Debugf("开始估算%s链交易gas费用（包含用户交易和gas转账），交易哈希: %s", pm.chainName, tx.TxHash)

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return nil, fmt.Errorf("raw error: %w", err)
	}

	// 步骤1: 计算用户原始交易的gas费用
	userGasLimit := evmTx.Gas()
	userGasPrice := evmTx.GasPrice()
	userGasWei := new(big.Int).Mul(big.NewInt(int64(userGasLimit)), userGasPrice)

	pm.log.Debugf("用户交易gas估算 - GasLimit: %d, GasPrice: %s wei, 总费用: %s wei",
		userGasLimit, userGasPrice.String(), userGasWei.String())

	// 步骤2: 估算gas转账交易的费用
	gasTransferFee, err := pm.estimateGasTransferFee(ctx, evmTx)
	if err != nil {
		pm.log.Warnf("估算gas转账费用失败，使用默认值: %v", err)
		// 使用默认的gas转账费用作为后备方案
		gasTransferFee = pm.getDefaultGasTransferFee(userGasPrice)
	}

	pm.log.Debugf("gas转账交易费用估算: %s wei", gasTransferFee.String())

	// 步骤3: 计算L1 data fee（仅适用于Optimism链）
	var l1DataFee *big.Int
	if pm.chainIndex == constant.OptimismChainIndex {
		l1DataFee, err = pm.estimateOptimismL1DataFee(ctx, tx.RawTxHex)
		if err != nil {
			pm.log.Warnf("估算Optimism L1 data fee失败，使用默认值: %v", err)
			l1DataFee = pm.getDefaultOptimismL1DataFee()
		}
		pm.log.Debugf("Optimism L1 data fee估算: %s wei", l1DataFee.String())
	} else {
		l1DataFee = big.NewInt(0) // 其他EVM链无L1 data fee
	}

	// 步骤4: 计算总gas费用（用户交易 + gas转账 + L1 data fee）
	totalGasWei := new(big.Int).Add(userGasWei, gasTransferFee)
	totalGasWei.Add(totalGasWei, l1DataFee)
	totalGasDecimal := decimal.NewFromBigInt(totalGasWei, 0)

	// 步骤4: 获取原生代币价格
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, pm.chainIndex, "")
	if err != nil {
		return nil, fmt.Errorf("get price error: %w", err)
	}
	// 检查价格是否过期
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("price expired: %d", timeUnix)
	}

	// 步骤5: 计算USDT价值
	// gas费用计算考虑链特定的USDT精度差异
	// 注意：gas pool系统统一使用6位精度USDT进行计算，与链上USDT代币精度无关
	decimals, _ := pm.getUsdtDecimals()
	decimalUsdt := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimals)))
	baseUnit := pm.getBaseUnit()
	gasInNativeToken := totalGasDecimal.Div(baseUnit)
	gasUSDT := gasInNativeToken.Mul(price).Mul(decimalUsdt) // decimalsUSDT = 10^6 (gas pool标准精度)

	// 记录链特定的gas费用计算信息
	chainName := constant.GetChainName(pm.chainIndex)
	pm.log.Debugf("%s链gas费用计算 - 原生代币数量: %s, 价格: %s USDT, gas费用: %s USDT (gas pool 6位精度)",
		chainName, gasInNativeToken.String(), price.String(), gasUSDT.String())

	pm.log.Debugf("%s链gas估算完成 - 用户交易: %s wei, gas转账: %s wei, 总计: %s wei, Price: %s USDT, GasUSDT: %s",
		pm.chainName, userGasWei.String(), gasTransferFee.String(), totalGasDecimal.String(), price.String(), gasUSDT.String())
	gas := &gaspool.UserTxGas{
		Gas:           totalGasDecimal,
		GasUSDT:       gasUSDT,
		Price:         price,
		PriceTimeUnix: timeUnix,
	}
	return gas, nil
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// TODO 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("%s链交易处理完成，耗时: %v", pm.chainName, duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return err
	}
	txHash, err := pm.sendTransaction(ctx, evmTx)
	if err != nil {
		return err
	}
	// 更新交易哈希
	tx.TxHash = txHash

	//  创建等待确认记录
	record := &EvmGasTransferWaitConfirmRecord{
		ChainIndex: tx.ChainIndex,
		TxID:       tx.ID,
		TxHash:     txHash,
		TimeUnix:   time.Now().Unix(),
	}
	if err = pm.repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record); err != nil {
		pm.log.Errorf("创建gas转账等待确认记录失败: %v: %+v", err, record)
		return err
	}

	return
}

// SendSponsorTx 发送原始交易到EVM网络（异步确认模式）
//
// 功能说明：
// 1. 解析原始交易，获取发送者地址
// 2. 从热钱包向交易发送者转账gas费用（基于tx.Gas字段计算）
// 3. 创建等待确认记录，等待gas转账确认后再发送用户交易
//
// 参数：
//   - ctx: 上下文对象
//   - tx: 包含原始交易数据和gas信息的sponsor交易
//
// 返回值：
//   - update: 更新后的sponsor交易（状态为prepare）
//   - err: 错误信息
//
// 注意：此方法实现异步确认模式，gas转账确认后才会发送用户交易
func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("%s链交易处理完成，耗时: %v", pm.chainName, duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return err
	}

	// 获取交易发送者地址
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		pm.log.Errorf("获取交易发送者地址失败，交易ID: %d, 错误: %v", tx.ID, err)
		return fmt.Errorf("failed to get transaction sender: %w", err)
	}

	pm.log.Debugf("交易准备完成，发送者地址: %s，开始处理gas费用转账", sender.Hex())
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID

	// 步骤1: 从热钱包向交易发送者转账gas费用
	//gasTransferTxHash, err := pm.transferGasToSender(ctx, sender, tx.Gas)
	//if err != nil {
	//	pm.log.Errorf("向交易发送者转账gas费用失败，交易ID: %d, 发送者: %s, 错误: %v",
	//		tx.ID, sender.Hex(), err)
	//	if !tx.TxType.IsPreReduceGasPool() {
	//		_, refundGasErr := pm.gpMgr.RefundGasPool(ctx, tx.ReduceFlowID, tx.Gas)
	//		if refundGasErr != nil {
	//			pm.log.Errorf("退款交易ID: %d, 发送者: %s, 错误: %v",
	//				tx.ID, sender.Hex(), err)
	//		}
	//	}
	//
	//	return fmt.Errorf("failed to transfer gas to sender: %w", err)
	//}
	gasTransferTxHash := "0x111111111111111"

	update.NativeTxHash = gasTransferTxHash
	pm.log.Infof("成功向发送者 %s 转账gas费用，转账交易哈希: %s，等待确认后发送用户交易",
		sender.Hex(), gasTransferTxHash)

	// TODO 异步等待
	//txHash, err := pm.sendTransaction(ctx, evmTx)
	//if err != nil {
	//	update.Reason = "broadcast fail:" + err.Error()
	//	update.Status = model.GasPoolTxStatusFail
	//	update.TxHash = txHash
	//	err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
	//	if err != nil {
	//		pm.log.Errorf("UpdateGasPoolSponsorTx by fail: %v: txID=%d, reason=%s", err, update.ID, update.Reason)
	//	}
	//	return err
	//}
	//
	//// 步骤3: 返回prepare状态，等待异步确认
	//tx.TxHash = txHash

	update.Status = model.GasPoolTxStatusWaitGas
	//update.TxHash = txHash
	if terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); terr != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", terr, update)
	}
	//  创建等待确认记录
	record := &EvmGasTransferWaitConfirmRecord{
		ChainIndex: tx.ChainIndex,
		TxID:       tx.ID,
		TxnType:    SendGasTxn,
		TxHash:     gasTransferTxHash,
		TimeUnix:   time.Now().Unix(),
	}
	if err = pm.repo.CreateEvmGasTransferWaitConfirmRecord(ctx, record); err != nil {
		pm.log.Errorf("创建gas转账等待确认记录失败: %v: %+v", err, record)
	}
	//if tx.TxType == model.GasPoolTxTypeDepositPreReduceGas {
	//	depositAmount := tx.ValueUSDT.Sub(tx.GasUSDT)
	//	if depositAmount.IsPositive() {
	//		// deposit
	//		flow, err := pm.gpMgr.DepositGasPool(ctx, tx.UserID, depositAmount, tx.ChainIndex, tx.TxHash, tx.Contract)
	//		if err != nil {
	//			pm.log.Errorf("deposit gas pool failed: %v: txID=%d, depositAmount=%s", depositAmount.String())
	//		} else {
	//			update.DepositFlowID = flow.ID
	//		}
	//	} else {
	//		pm.log.Errorf("pre reduce gas deposit amount less than or equal to zero: txID=%d, depositAmount=%s",
	//			update.ID, depositAmount.String())
	//	}
	//}
	pm.log.Infof("%s链交易进入异步确认模式，交易ID: %d，gas转账哈希: %s",
		pm.chainName, tx.ID, gasTransferTxHash)
	return nil
}

// Start 启动EVM paymaster异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) Start(ctx context.Context) error {
	if pm.stopCh == nil {
		pm.stopCh = make(chan struct{})
	}

	go func() {
		t := time.NewTicker(time.Second * 3)
		defer t.Stop()

		pm.log.Infof("启动%s链paymaster异步任务: gas转账确认", pm.chainName)

		var (
			gasTransferWaitSecs int64 = DefaultGasTransferWaitSeconds
		)

		for {
			select {
			case <-pm.stopCh:
				pm.log.Infof("停止%s链paymaster异步任务", pm.chainName)
				return
			case <-t.C:
				pm.handleGasTransferConfirm(ctx, gasTransferWaitSecs)
			}
		}
	}()

	return nil
}

// Stop 停止EVM paymaster异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) Stop(ctx context.Context) error {
	if pm.stopCh != nil {
		close(pm.stopCh)
		pm.log.Infof("%s链paymaster异步任务已停止", pm.chainName)
	}
	return nil
}
