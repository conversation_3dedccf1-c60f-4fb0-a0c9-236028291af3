package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"encoding/hex"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

// transaction.go - EVM paymaster 交易处理相关功能
// 包含交易解码、发送用户交易、gas转账等核心交易处理逻辑

// decodeTransferTx 解码转账交易
// 参数:
//   - ctx: 上下文对象
//   - rawTx: 原始交易数据
//
// 返回值:
//   - *gaspool.UserTx: 解码后的用户交易
//   - error: 错误信息
func (pm *Paymaster) decodeTransferTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*gaspool.UserTx, error) {
	evmTx, err := utils.RlpDecodeBytes(rawTxHex)
	if err != nil {
		return nil, fmt.Errorf("failed to decode raw tx: %w", err)
	}

	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tx sender: %w", err)
	}

	chainID := constant.GetChainID(pm.chainIndex)
	if chainID == 0 {
		return nil, fmt.Errorf("invalid chainIndex: %d", pm.chainIndex)
	}

	// 默认使用原生转账的 to 和 value
	toAddr := evmTx.To().Hex()
	value := decimal.NewFromBigInt(evmTx.Value(), 0)
	contract := ""

	// 检查是否是 ERC20 Transfer
	if data := evmTx.Data(); len(data) >= 4 { // 至少包含 methodID（4字节）
		methodID := hex.EncodeToString(data[:4])
		if methodID == constant.ERC20TransferMethodID && len(data) >= 68 { // 4(methodID) + 32(to) + 32(value)
			// 解析 to 地址（跳过前 12 字节的 0 填充）
			to := common.BytesToAddress(data[16:36]).Hex()
			// 解析转账金额
			amount := new(big.Int).SetBytes(data[36:68])
			value = decimal.NewFromBigInt(amount, 0)
			toAddr = to
			contract = evmTx.To().Hex() // 合约地址
		}
	}
	return &gaspool.UserTx{
		From:       sender.Hex(),
		To:         toAddr,
		Value:      value,
		ChainIndex: pm.chainIndex,
		RawTxHex:   rawTxHex,
		Contract:   contract,
		TxType:     txType,
		TxHash:     evmTx.Hash().Hex(),
	}, nil
}

// sendUserTransaction 发送用户交易
// 参数:
//   - ctx: 上下文对象
//   - stx: sponsor交易数据
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) sendUserTransaction(ctx context.Context, stx *model.GasPoolSponsorTx) error {
	// 解码EVM交易
	evmTx, err := utils.RlpDecodeBytes(stx.RawTxHex)
	if err != nil {
		return fmt.Errorf("failed to decode EVM transaction: %w", err)
	}

	// 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return fmt.Errorf("failed to get %s chain client: %w", pm.chainName, err)
	}

	// 发送用户交易
	err = client.SendTransaction(ctx, evmTx)
	if err != nil {
		return fmt.Errorf("failed to send %s chain user transaction: %w", pm.chainName, err)
	}

	// 更新交易状态为pending
	update := &model.GasPoolSponsorTx{
		Model:  stx.Model,
		Status: model.GasPoolTxStatusPending,
		TxHash: evmTx.Hash().Hex(),
	}

	err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
	if err != nil {
		pm.log.Errorf("更新%s链sponsor交易状态失败: %v: %d: %s",
			pm.chainName, err, stx.ID, update.Status)
	}

	pm.log.Infof("成功发送%s链用户交易，交易ID: %d，交易哈希: %s",
		pm.chainName, stx.ID, evmTx.Hash().Hex())

	return nil
}

// transferGasToSender 从热钱包向交易发送者转账gas费用
//
// 重要说明：
//  1. 交易格式选择：本方法使用传统交易格式（Type 0）而非EIP-1559格式（Type 2）
//     原因：gas转账是关键操作，需要确保在所有EVM兼容链上都有最佳兼容性
//     EIP-1559虽然可以优化费用，但在某些网络或钱包中可能存在兼容性问题
//
// 2. Gas估算策略：使用eth_estimateGas RPC调用进行动态估算
//   - 基础估算：调用EstimateGas获取网络建议的gas limit
//   - 安全系数：应用链特定的倍数（通常1.2-1.3倍）作为缓冲
//   - 回退机制：估算失败时使用21000作为标准ETH转账的默认值
//
// 3. Gas价格策略：
//   - 获取网络建议的gas价格（SuggestGasPrice）
//   - 应用1.2倍数确保快速确认，避免用户交易延迟
//   - 回退到默认值（baseFee + tip）当网络调用失败时
//
// 4. 链特定考虑：
//   - Optimism：由于L1 data fee，使用稍高的安全系数（1.3倍）
//   - 其他EVM链：使用标准安全系数（1.2倍）
//   - BSC：与其他EVM链保持一致的处理逻辑
//
// 参数:
//   - ctx: 上下文对象
//   - senderAddr: 交易发送者地址
//   - gasAmount: 需要转账的gas金额（以decimal格式表示）
//
// 返回值:
//   - string: 转账交易的哈希值
//   - error: 错误信息
func (pm *Paymaster) transferGasToSender(ctx context.Context, senderAddr common.Address, gasAmount decimal.Decimal) (string, error) {
	pm.log.Debugf("开始从热钱包向发送者 %s 转账gas费用，金额: %s wei", senderAddr.Hex(), gasAmount.String())

	// 步骤0: 验证gas金额
	gasAmountWei := gasAmount.BigInt()
	if gasAmountWei.Sign() <= 0 {
		pm.log.Errorf("gas金额无效: %s", gasAmount.String())
		return "", fmt.Errorf("invalid gas amount: %s", gasAmount.String())
	}

	// 步骤1: 获取热钱包私钥
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取%s链热钱包私钥失败: %v", pm.chainName, err)
		return "", fmt.Errorf("failed to get hot wallet private key: %w", err)
	}

	// 步骤2: 从私钥获取热钱包地址
	hotWalletAddr, privateKey, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		pm.log.Errorf("从私钥解析热钱包地址失败: %v", err)
		return "", fmt.Errorf("failed to get address from private key: %w", err)
	}

	pm.log.Debugf("热钱包地址: %s，准备向 %s 转账", hotWalletAddr.Hex(), senderAddr.Hex())

	// 步骤3: 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取%s链客户端失败: %v", pm.chainName, err)
		return "", fmt.Errorf("failed to get EVM client: %w", err)
	}

	// 步骤4: 获取链ID
	chainID, err := client.ChainID(ctx)
	if err != nil {
		pm.log.Errorf("获取%s链ID失败: %v", pm.chainName, err)
		return "", fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 步骤5: 获取热钱包的nonce
	nonce, err := client.PendingNonceAt(ctx, hotWalletAddr)
	if err != nil {
		pm.log.Errorf("获取热钱包nonce失败: %v", err)
		return "", fmt.Errorf("failed to get nonce: %w", err)
	}
	// 步骤6: 获取当前网络gas价格并应用1.2x倍数策略
	// 注意：为了兼容性和稳定性，gas转账交易使用传统格式而非EIP-1559
	// 这样可以避免在某些网络或钱包中出现兼容性问题
	suggestedGasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		pm.log.Warnf("获取建议gas价格失败，使用默认值: %v", err)
		// 使用传统的gas价格计算方式：baseFee + tip
		suggestedGasPrice = big.NewInt(constant.DefaultBaseFee + constant.DefaultGasTipCap)
	}

	// 应用1.2x倍数策略以确保更快的交易确认
	// 对于gas转账交易，我们需要确保快速确认以避免用户交易延迟
	gasPriceMultiplier := decimal.NewFromFloat(DefaultGasLimitMultiplier)
	adjustedGasPrice := decimal.NewFromBigInt(suggestedGasPrice, 0).Mul(gasPriceMultiplier).BigInt()

	pm.log.Debugf("gas价格策略 - 原始gas价格: %s wei, 调整后gas价格: %s wei (应用1.2x倍数)",
		suggestedGasPrice.String(), adjustedGasPrice.String())

	// 步骤7: 使用EstimateGas进行动态gas limit估算
	// 使用传统交易格式进行估算，确保与实际发送的交易格式一致
	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:     hotWalletAddr,
		To:       &senderAddr,
		GasPrice: adjustedGasPrice, // 使用传统gas价格字段
		Value:    gasAmountWei,
		Data:     nil, // 原生代币转账不需要data
	})
	if err != nil {
		pm.log.Warnf("gas limit估算失败，使用默认值26000: %v", err)
		estimatedGasLimit = DefaultGasLimit // 回退到标准ETH转账的gas limit
	}

	// 应用安全系数到gas limit（使用链特定的倍数）
	// 不同链可能需要不同的安全系数，特别是L2链
	gasLimitMultiplier := pm.getChainSpecificGasMultiplier()
	safeGasLimit := decimal.NewFromInt(int64(estimatedGasLimit)).Mul(gasLimitMultiplier)
	gasLimit := safeGasLimit.BigInt().Uint64()

	pm.log.Debugf("gas limit估算 - 估算值: %d, 安全系数: %s, 最终gas limit: %d",
		estimatedGasLimit, gasLimitMultiplier.String(), gasLimit)

	// 步骤8: gas金额已在方法开始时验证和转换

	pm.log.Debugf("创建传统格式转账交易 - From: %s, To: %s, Value: %s wei, Nonce: %d, GasLimit: %d, GasPrice: %s wei",
		hotWalletAddr.Hex(), senderAddr.Hex(), gasAmountWei.String(), nonce, gasLimit, adjustedGasPrice.String())

	// 创建传统格式交易（Type 0），而非EIP-1559交易
	// 这样可以确保在所有EVM兼容链上都有良好的兼容性
	// 特别是对于gas转账这种关键操作，稳定性比费用优化更重要
	tx := types.NewTransaction(
		nonce,
		senderAddr,
		gasAmountWei,
		gasLimit,
		adjustedGasPrice,
		nil, // 原生代币转账不需要data
	)

	// 步骤9: 签名交易
	// 使用传统签名器而非London签名器，与交易类型保持一致
	signedTx, err := types.SignTx(tx, types.LatestSignerForChainID(chainID), privateKey)
	if err != nil {
		pm.log.Errorf("签名转账交易失败: %v", err)
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// 步骤10: 发送转账交易
	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		pm.log.Errorf("发送gas转账交易失败: %v", err)
		return "", fmt.Errorf("failed to send gas transfer transaction: %w", err)
	}

	txHash := signedTx.Hash().Hex()

	// 计算实际交易费用用于日志记录
	actualTxFee := new(big.Int).Mul(new(big.Int).SetUint64(gasLimit), adjustedGasPrice)

	pm.log.Infof("成功发送gas转账交易 - 哈希: %s, 从 %s 向 %s 转账 %s wei, gas策略: 1.2x倍数, gasLimit: %d, gasPrice: %s wei, 预估交易费用: %s wei",
		txHash, hotWalletAddr.Hex(), senderAddr.Hex(), gasAmountWei.String(), gasLimit, adjustedGasPrice.String(), actualTxFee.String())

	return txHash, nil
}

func (pm *Paymaster) sendTransaction(ctx context.Context, tx *types.Transaction) (string, error) {
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return "", fmt.Errorf("failed to get EVM client: %w", err)
	}
	err = client.SendTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}
	txHash := tx.Hash().Hex()
	return txHash, nil
}
