package solana

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	solanaCom "byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/gagliardetto/solana-go/rpc/jsonrpc"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

// paymaster.go - Solana paymaster 主文件
// 包含 paymaster 结构体定义、构造函数和核心接口实现
// 支持 Solana 链的交易处理、签名验证、费用估算等功能

// Solana paymaster 相关常量
const (
	DefaultPriceExpireSeconds = 60 // 默认价格过期时间（秒）
)

var decimalsUSDT = decimal.NewFromInt(1000000)

type Paymaster struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	solCli           *solanaCom.SmartNodeSelectionClient
	gpMgr            base.GasPoolMgr
	stxMgr           base.GasPoolSponsorTxMgr
	dAddrMgr         base.GasPoolDepositAddressMgr // 新增：用于访问充值地址查询等功能
	rd               redis.UniversalClient         // 新增：Redis 客户端，用于 ATA owner 缓存
	ataOwnerRepo     base.SolanaATAOwnerRepo       // 新增：ATA owner 数据库操作接口
	// 缓存字段，避免重复计算
	cachedPrivateKey *solana.PrivateKey
	cachedPublicKey  *solana.PublicKey
	stopCh           chan struct{}
	chainName        string
	// 配置字段
	priceExpireSeconds int64 // 价格过期时间（秒）
}

// NewPaymaster 创建新的Solana paymaster实例
func NewPaymaster(logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	hotAccountReader base.HotAccountReader,
	solCli *solanaCom.SmartNodeSelectionClient,
	gpMgr base.GasPoolMgr,
	stxMgr base.GasPoolSponsorTxMgr,
	dAddrMgr base.GasPoolDepositAddressMgr, // 新增：用于访问充值地址查询等功能
	rd redis.UniversalClient, // 新增：Redis 客户端，用于 ATA owner 缓存
	ataOwnerRepo base.SolanaATAOwnerRepo, // 新增：ATA owner 数据库操作接口
) *Paymaster {
	// 获取链名称，用于日志输出和任务标识
	chainName := constant.GetChainName(constant.SolChainIndex)
	if chainName == "" {
		chainName = "Solana" // 默认链名称
	}

	return &Paymaster{
		log:                log.NewHelper(logger),
		tokenPriceReader:   tokenPriceReader,
		hotAccountReader:   hotAccountReader,
		solCli:             solCli,
		gpMgr:              gpMgr,
		stxMgr:             stxMgr,
		dAddrMgr:           dAddrMgr,
		rd:                 rd,           // 新增：Redis 客户端初始化
		ataOwnerRepo:       ataOwnerRepo, // 新增：ATA owner 数据库操作接口初始化
		chainName:          chainName,    // 修复：添加chainName字段初始化
		priceExpireSeconds: DefaultPriceExpireSeconds,
	}
}

// SetPriceExpireSeconds 设置价格过期时间（秒）
func (pm *Paymaster) SetPriceExpireSeconds(seconds int64) {
	if seconds > 0 {
		pm.priceExpireSeconds = seconds
		pm.log.Infof("价格过期时间已更新为: %d 秒", seconds)
	}
}

// DecodeUserTx 解码用户交易数据
func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (tx *gaspool.UserTx, err error) {
	// 使用统一的验证函数
	if err := validateRawTx(rawTxHex); err != nil {
		return nil, err
	}

	tx, err = pm.decodeTransferTx(ctx, rawTxHex)
	if err != nil {
		return nil, wrapError("decode transfer transaction", err)
	}

	tx.TxType = txType
	tx.RawTxHex = rawTxHex
	tx.ChainIndex = constant.SolChainIndex
	return tx, nil
}

// VerifyUserTxSignature 验证用户交易签名
func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	return pm.verifyTxSignature(tx.RawTxHex)
}

// EstimateGas 估算交易gas费用
func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	// 使用统一的验证函数
	if err := validateUserTx(tx); err != nil {
		return nil, err
	}

	// 使用统一的交易准备函数
	transaction, err := prepareTransactionFromHex(tx.RawTxHex)
	if err != nil {
		return nil, wrapError("prepare transaction for gas estimation", err)
	}

	// 获取交易费用（以lamports为单位）
	fee, err := pm.GetTransactionFeeWithContext(ctx, transaction.Message.ToBase64())
	if err != nil {
		return nil, wrapError("get transaction fee", err)
	}

	// 计算租金费用（Solana 特有的账户租金机制）
	// Solana 要求新创建的账户必须有足够的 SOL 余额来支付租金，或者达到免租金状态
	rentFee, err := pm.CalculateRentFeeForTransaction(ctx, transaction)
	if err != nil {
		pm.log.Warnf("计算租金费用失败: %v，将使用零租金费用", err)
		return nil, wrapError("calculate rent fee for transaction", err)
	}

	// 总费用 = 交易费用 + 租金费用
	totalFee := fee.Add(rentFee)
	// 获取SOL价格
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, constant.SolChainIndex, "")
	if err != nil {
		return nil, wrapError("get SOL price", err)
	}
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("SOL price expired at timestamp: %d", timeUnix)
	}

	// 计算USDT费用（基于总费用，包含交易费用和租金费用）
	gasUSDT := totalFee.Div(constant.BaseUnitPerSOL).Mul(price).Mul(decimalsUSDT)
	gas := &gaspool.UserTxGas{
		Gas:           totalFee, // 使用包含租金费用的总费用
		GasUSDT:       gasUSDT,
		ActivateFee:   rentFee,
		Price:         price,
		PriceTimeUnix: timeUnix,
	}

	pm.log.Infof("Solana 交易费用估算完成 - 总费用: %s lamports (%.6f SOL), USDT 价值: %s",
		totalFee.String(),
		totalFee.Div(constant.BaseUnitPerSOL).InexactFloat64(),
		gasUSDT.String())

	return gas, nil
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("Solana交易处理完成，耗时: %v", duration)
	}()

	// 使用统一的验证函数
	if err = validateSponsorTx(tx); err != nil {
		if tx != nil {
			pm.log.Errorf("验证失败: %v", err)
		}
		return
	}

	// 使用统一的交易准备函数
	transaction, err := prepareTransactionFromHex(tx.RawTxHex)
	if err != nil {
		err = wrapErrorWithID("prepare transaction for deposit", int64(tx.UserID), err)
		return
	}
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID
	// 发送交易到Solana网络
	sig, err := pm.solCli.Select().SendTransactionWithOpts(
		ctx,
		transaction,
		rpc.TransactionOpts{
			SkipPreflight:       false,
			PreflightCommitment: rpc.CommitmentFinalized,
		},
	)
	if err != nil {
		err = fmt.Errorf("solana transaction broadcast failed: %v", err)
		update.Reason = "broadcast failed: " + err.Error()
		update.Status = model.GasPoolTxStatusFail
		update.TxHash = sig.String()
		err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
		if err != nil {
			pm.log.Errorf("UpdateGasPoolSponsorTx by fail: %v: txID=%d, reason=%s", err, update.ID, update.Reason)
		}
		return
	}

	tx.TxHash = sig.String()
	update.TxHash = sig.String()
	update.Status = model.GasPoolTxStatusSuccess
	err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
	if err != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by fail: %v: txID=%d, reason=%s", err, update.ID, update.Reason)
	}
	// deposit
	_, err = pm.gpMgr.DepositGasPool(ctx, tx.UserID, tx.ValueUSDT, tx.ChainIndex, tx.TxHash)
	if err != nil {
		pm.log.Errorf("DepositGasPool by sendRawTxByDepositWithoutGas: %v: userID=%d, valueUSDT=%s, chainIndex=%d, txHash=%s",
			err, tx.UserID, tx.ValueUSDT, tx.ChainIndex, tx.TxHash)
	}
	return
}

// SendSponsorTx 发送原始交易到Solana网络
func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("Solana交易处理完成，耗时: %v", duration)
	}()

	// 使用统一的验证函数
	if err = validateSponsorTx(tx); err != nil {
		if tx != nil {
			pm.log.Errorf("交易ID %d 验证失败: %v", tx.ID, err)
		}
		return
	}
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID
	// 使用统一的交易准备函数
	transaction, err := prepareTransactionFromHex(tx.RawTxHex)
	if err != nil {
		err = wrapErrorWithID("prepare transaction for sponsor", int64(tx.ID), err)
		return
	}

	// 为交易添加paymaster和用户签名
	if err = pm.signTransaction(transaction); err != nil {
		err = wrapErrorWithID("sign transaction", int64(tx.ID), err)
		return
	}

	// 发送交易到Solana网络
	sig, err := pm.solCli.Select().SendTransactionWithOpts(
		ctx,
		transaction,
		rpc.TransactionOpts{
			SkipPreflight:       false,
			PreflightCommitment: rpc.CommitmentFinalized,
		},
	)
	if err != nil {
		pm.log.Errorf("发送Solana交易失败，交易ID: %d, 错误: %v", tx.ID, err)
		// Try to extract the structured error message
		errMsg := err.Error() // default to the error string
		// 尝试解析RPCError结构
		if rpcErr, ok := err.(*jsonrpc.RPCError); ok {
			// 使用RPCError中的Message作为主要错误信息
			errMsg = rpcErr.Message
		}

		err = fmt.Errorf("solana transaction send failed: %v", err)
		update.Reason = "broadcast failed: " + err.Error()
		update.Status = model.GasPoolTxStatusFail
		//update.TxHash = sig.String()
		upErr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
		if upErr != nil {
			pm.log.Errorf("UpdateGasPoolSponsorTx by fail: %v: txID=%d, reason=%s", err, update.ID, update.Reason)
		}
		return fmt.Errorf("%s", errMsg)
	}

	tx.TxHash = sig.String()
	update.Status = model.GasPoolTxStatusSuccess
	update.TxHash = sig.String()
	if terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); terr != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", terr, update)
	}

	if tx.TxType == model.GasPoolTxTypeDepositPreReduceGas {
		depositAmount := tx.ValueUSDT.Sub(tx.GasUSDT)
		if depositAmount.IsPositive() {
			// deposit
			flow, err := pm.gpMgr.DepositGasPool(ctx, tx.UserID, depositAmount, tx.ChainIndex, tx.TxHash)
			if err != nil {
				pm.log.Errorf("deposit gas pool failed: %v: txID=%d, depositAmount=%s", depositAmount.String())
			} else {
				update.DepositFlowID = flow.ID
			}
		} else {
			pm.log.Errorf("pre reduce gas deposit amount less than or equal to zero: txID=%d, depositAmount=%s",
				update.ID, depositAmount.String())
		}
	}
	pm.log.Infof("Solana交易发送成功，交易ID: %d, 交易哈希: %s", tx.ID, sig.String())
	return nil
}

// Start 启动EVM paymaster异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) Start(ctx context.Context) error {
	return nil

}

// Stop 停止EVM paymaster异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) Stop(ctx context.Context) error {
	if pm.stopCh != nil {
		close(pm.stopCh)
		pm.log.Infof("%s链paymaster异步任务已停止", pm.chainName)
	}
	return nil
}
