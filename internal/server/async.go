package server

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type AsyncTask interface {
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

type AsyncServer struct {
	log *log.Helper

	tasks map[string]AsyncTask
}

func NewAsyncServer(
	logger log.Logger,
	spotPriceMgr *biz.SpotPriceManager,
	tronPM *tron.Paymaster,
	vemPM *evm.Paymaster,
) *AsyncServer {
	return &AsyncServer{
		log: log.NewHelper(logger),
		tasks: map[string]AsyncTask{
			"spot price":     spotPriceMgr,
			"tron paymaster": tronPM,
			"evm paymaster":  vemPM,
		},
	}
}

func (s *AsyncServer) Start(ctx context.Context) error {
	var cf func()
	for taskName, task := range s.tasks {
		s.log.Infof("async server start task: %s", taskName)
		if err := task.Start(ctx); err != nil {
			if cf != nil {
				cf()
			}
			return err
		}
		tmpCf := func() {
			task.Stop(ctx)
		}
		if cf == nil {
			cf = tmpCf
		} else {
			oldCf := cf
			cf = func() {
				oldCf()
				tmpCf()
			}
		}
	}
	return nil
}

func (s *AsyncServer) Stop(ctx context.Context) error {
	for taskName, task := range s.tasks {
		s.log.Infof("async server stop task: %s", taskName)
		if err := task.Stop(ctx); err != nil {
			s.log.Errorf("async server stop task error: %v: taskName=%s", err, taskName)
		}
	}
	return nil
}
